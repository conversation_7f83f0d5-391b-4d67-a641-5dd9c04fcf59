package com.aic.app.vo;

import com.aic.app.model.Product;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class ProductVo {

    private Long id;

    @Schema(description = "产品类型，1-预售节点 2-普通商品")
    private int type;

    @Schema(description = "产品名称")
    private String name;

    @Schema(description = "产品描述")
    private String description;

    @Schema(description = "收益率,显示的时候需要乘100")
    private BigDecimal rate;

    @Schema(description = "手续费")
    private BigDecimal fee;

    @Schema(description = "价格")
    private BigDecimal price;

    @Schema(description = "天数")
    private int day;
    
    @Schema(description = "图片")
    private String image;

    public ProductVo(Product row) {
        this.id = row.getId();
        this.type = row.getType();
        this.name = row.getName();
        this.description = row.getDescription();
        this.rate = row.getRate();
        this.fee = row.getFee();
        this.day = row.getDay();
        this.price = row.getPrice();
        this.image = row.getImage();
    }
}
